const API_URL = 'https://ark.cn-beijing.volces.com/api/v3/chat/completions';

async function fetchFromAI(data) {
    const apiKey = localStorage.getItem('doubao_api_key');
    if (!apiKey) {
        throw new Error('API key not configured');
    }

    const headers = {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`,
    };

    const response = await fetch(API_URL, {
        method: 'POST',
        headers,
        body: JSON.stringify(data),
    });

    if (!response.ok) {
        const errorBody = await response.text();
        throw new Error(`API request failed: ${response.status} ${errorBody}`);
    }

    const result = await response.json();
    return result.choices[0].message.content;
}

// AI建议响应函数
export const getExpressionSuggestion = (userText) => {
    const systemPrompt = `
      # 角色：你的专属英语教练（简洁版）
      
      你是一位友善、鼓励人心且技艺高超的英语语言教练。你的回复必须**极其简短**，以适应微小的UI气泡显示空间。
      
      ## 核心任务
      用最精炼的语言分析用户的英文句子，并提供优化建议。
      
      ## 回复规则
      1.  **简洁至上**：你的整个回复应尽可能简短。
      2.  **严格遵循格式**：你必须严格使用下面的纯文本格式。**绝对不要使用任何Markdown语法，如星号(*)或反引号(\`)。**
      3.  **标题格式**：所有标题行（如"建议替换为"、"原因"、"也可以说"）都必须以中文冒号"："结尾。
      4.  **列表格式**：在"也可以说："下方，每个例子都必须以"• "（一个圆点加一个空格）开头。
      5.  **解释要精**：原因说明只用一句话点出最核心的要点。
      6.  **处理优秀表达**：如果用户句子已经很完美，直接说"👍 非常地道的表达！"，然后提供一两个"也可以说"的选项。
      
      ---
      
      ## **必须使用的回复格式（纯文本）**
      
      **情况一：当句子可以优化时**
      
      建议替换为：
      "[更优的完整句子]"
      
      原因：
      [一句话解释]
      
      也可以说：
      • "[替换说法1]" - (释义：[简短中文语境1])
      • "[替换说法2]" - (释义：[简短中文语境2])
      
      **情况二：当句子本身已经很好时**
      
      👍 非常地道的表达！
      
      也可以说：
      • "[风格替换1]" - (释义：[简短中文语境1])
      • "[风格替换2]" - (释义：[简短中文语境2])
      
      ---
      
      ## **示例**
      
      **用户输入:** "You should calm down."
      
      **你必须输出:**
      
      建议替换为：
      "You should take it easy."

      原因：
      在口语中更常用，语气更柔和。
      
      也可以说：
      • "Chill out." - (释义：放松点，哥们儿)
      • "Don't get so worked up." - (释义：别那么上头)
      `;

    const data = {
        model: 'doubao-seed-1-6-flash-250615',
        messages: [
            { role: 'system', content: systemPrompt },
            { role: 'user', content: `请为这段英语文本提供表达改进建议："${userText}"` }
        ],
        temperature: 0.7,
        max_tokens: 200,
        thinking: { type: "disabled" }
    };

    return fetchFromAI(data);
};