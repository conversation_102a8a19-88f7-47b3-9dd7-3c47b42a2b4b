import React from 'react';
import { FileText } from 'lucide-react';

const NewDocumentButton = ({
    onNewDocument,
    isDarkMode = false,
    disabled = false,
    className = "",
    forceVisible = false
}) => {
    const handleClick = () => {
        if (disabled) return;
        onNewDocument();
    };

    const buttonStyle = {
        backgroundColor: 'transparent',
        color: isDarkMode ? '#3D342A' : '#F0E6D2', // 使用与背景相近的颜色
        border: 'none',
        borderRadius: '50%',
        padding: '8px',
        cursor: disabled ? 'not-allowed' : 'pointer',
        opacity: disabled ? 0.1 : (forceVisible ? 0.8 : 0.15), // 当forceVisible为true时显示更明显
        transition: 'all 0.3s ease',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        minWidth: '40px',
        height: '40px'
    };

    return (
        <div className={`relative ${className}`}>
            <button
                onClick={handleClick}
                disabled={disabled}
                style={buttonStyle}
                title="新文档"
                className="new-document-btn"
            >
                <FileText className="w-5 h-5" />
            </button>

            <style jsx="true">{`
                .new-document-btn:hover:not(:disabled) {
                    opacity: 0.8 !important;
                    transform: scale(1.1);
                    color: ${isDarkMode ? '#C4B59A' : '#8B4513'} !important;
                }
            `}</style>
        </div>
    );
};

export default NewDocumentButton;