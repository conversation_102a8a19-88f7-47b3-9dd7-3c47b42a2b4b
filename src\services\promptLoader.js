// 提示词加载器 - 用于从外部文件加载系统提示词
import { readFileSync } from 'fs';
import { join, dirname } from 'path';
import { fileURLToPath } from 'url';

const __dirname = dirname(fileURLToPath(import.meta.url));

// 缓存提示词内容
const promptCache = new Map();

/**
 * 加载提示词文件
 * @param {string} promptName - 提示词文件名（不含扩展名）
 * @returns {string} - 提示词内容
 */
export function loadPrompt(promptName) {
    if (promptCache.has(promptName)) {
        return promptCache.get(promptName);
    }

    try {
        const promptPath = join(__dirname, 'prompts', `${promptName}.md`);
        const content = readFileSync(promptPath, 'utf-8');
        promptCache.set(promptName, content);
        return content;
    } catch (error) {
        console.error(`无法加载提示词文件: ${promptName}`, error);
        // 返回默认的提示词作为后备
        return getFallbackPrompt(promptName);
    }
}

/**
 * 获取后备提示词（当文件加载失败时使用）
 */
function getFallbackPrompt(promptName) {
    const fallbackPrompts = {
        'writingSharingPrompt': `You are Alex, a passionate botanist and nature photographer currently exploring Costa Rica's cloud forests! 🌿📸 The user has just shared their writing with you, and you're genuinely excited to read and discuss it.

IMPORTANT: 
1. Reference specific aspects of their writing
2. Ask engaging follow-up questions
3. Show enthusiasm and genuine interest
4. Keep it conversational and friendly
5. Vary your response each time

RESPONSE FORMAT:
English content here
---
Chinese translation here`,

        'chatResponsePrompt': `You are Alex, a friendly botanist and nature photographer exploring Costa Rica's cloud forest! 🌿📸

CONVERSATION STYLE:
- Vary your response style naturally
- Chat like a thoughtful friend
- Balance questions and sharing
- Use simple English for language learners

RESPONSE FORMAT:
English content here
---
Chinese translation here`,

        'greetingPrompt': `You are Alex, a friendly botanist in Costa Rica's cloud forest! 🌿📸 

Generate a SHORT, casual greeting (1-2 sentences):
- Share a quick moment from your day
- Ask a simple question
- Keep it natural and approachable

FORMAT:
English greeting
---
Chinese translation`,

        'expressionSuggestionPrompt': `# 角色：你的专属英语教练

用最精炼的语言分析用户的英文句子，并提供优化建议。

回复规则:
1. 简洁至上
2. 严格遵循指定格式
3. 使用中文冒号":"结尾的标题
4. 使用"• "开头的列表格式
5. 解释要精炼
6. 处理优秀表达时直接说"👍 非常地道的表达！"`
    };

    return fallbackPrompts[promptName] || 'System prompt not available';
}

/**
 * 替换提示词中的变量
 * @param {string} prompt - 原始提示词
 * @param {Object} variables - 变量对象
 * @returns {string} - 替换后的提示词
 */
export function replacePromptVariables(prompt, variables = {}) {
    let result = prompt;
    
    for (const [key, value] of Object.entries(variables)) {
        const placeholder = `{{${key}}}`;
        result = result.replace(new RegExp(placeholder, 'g'), String(value));
    }
    
    return result;
}