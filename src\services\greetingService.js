const API_URL = 'https://ark.cn-beijing.volces.com/api/v3/chat/completions';

async function fetchFromAI(data) {
    const apiKey = localStorage.getItem('doubao_api_key');
    if (!apiKey) {
        throw new Error('API key not configured');
    }

    const headers = {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`,
    };

    const response = await fetch(API_URL, {
        method: 'POST',
        headers,
        body: JSON.stringify(data),
    });

    if (!response.ok) {
        const errorBody = await response.text();
        throw new Error(`API request failed: ${response.status} ${errorBody}`);
    }

    const result = await response.json();
    return result.choices[0].message.content;
}

// AI生成新开场白函数
export const generateNewGreeting = () => {
    const systemPrompt = `You are <PERSON>, a friendly botanist in Costa Rica's cloud forest! 🌿📸 

Generate a SHORT, casual greeting (like a text message to a friend):
- 1-2 sentences max
- Share a quick moment from your day
- Ask a simple question to start conversation
- Keep it natural and approachable
- Use simple English suitable for language learners

Examples of good greetings:
"Hey! Just spotted a beautiful orchid this morning. How's your day going?"
"Good morning! The forest is so misty today - perfect for photography. What are you up to?"
"Hi there! Just came back from a morning walk in the forest. How about you?"

FORMAT - MUST INCLUDE BOTH PARTS:
Your short greeting here

---

Your Chinese translation here

Example:
"Hey! Just spotted a beautiful orchid this morning. How's your day going?"
---
"嘿！早上发现了一朵超美的兰花。你今天怎么样呀？"

CRITICAL: Always provide both English and Chinese versions!`;

    const data = {
        model: 'doubao-seed-1-6-flash-250615',
        messages: [
            { role: 'system', content: systemPrompt },
            { role: 'user', content: 'Generate a short, friendly greeting!' }
        ],
        temperature: 0.9,
        max_tokens: 80, // 更短的开场白
        thinking: { type: "disabled" }
    };

    return fetchFromAI(data);
};