const API_URL = 'https://ark.cn-beijing.volces.com/api/v3/chat/completions';
import { loadPrompt, replacePromptVariables } from './promptLoader.js';

async function fetchFromAI(data) {
    const apiKey = localStorage.getItem('doubao_api_key');
    if (!apiKey) {
        throw new Error('API key not configured');
    }

    const headers = {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`,
    };

    const response = await fetch(API_URL, {
        method: 'POST',
        headers,
        body: JSON.stringify(data)
    });

    if (!response.ok) {
        const errorBody = await response.text();
        throw new Error(`API request failed: ${response.status} ${errorBody}`);
    }

    const result = await response.json();
    return result.choices[0].message.content;
}

// 生成写作分享的个性化AI回复
export const generateWritingSharingResponse = async (contextData) => {
    // 从外部文件加载提示词
    let systemPrompt = loadPrompt('writingSharingPrompt');
    
    // 替换提示词中的变量
    systemPrompt = replacePromptVariables(systemPrompt, {
        title: contextData.title,
        contentPreview: `${contextData.content.substring(0, 200)}${contextData.content.length > 200 ? '...' : ''}`,
        wordCount: contextData.wordCount
    });

    const data = {
        model: 'doubao-seed-1-6-flash-250615',
        messages: [
            { role: 'system', content: systemPrompt },
            { role: 'user', content: `I'm sharing my writing with you: "${contextData.title}". Please give me an enthusiastic and personalized response!` }
        ],
        temperature: 0.9,
        max_tokens: 300,
        thinking: { type: "disabled" }
    };

    return fetchFromAI(data);
};